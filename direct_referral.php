<?php
$title = "Referral";
include_once 'header.php';
$query = "SELECT u.uid, u.login_id, u.name, u.mobile, u.datetime, s.login_id as sponsor, p.login_id as placement, u.position, u.package, u.topup FROM user as u"
    . " LEFT JOIN user as s ON s.uid=u.refer_id"
    . " LEFT JOIN user as p ON p.uid=u.placement_id"
    . " WHERE u.refer_id = '$uid'";
$result = my_query($query);

// Count total referrals
$total_referrals = mysqli_num_rows($result);

// Calculate total earnings from referrals (example calculation)
$total_earnings_query = "SELECT SUM(topup) as total FROM user WHERE refer_id = '$uid'";
$total_earnings_result = my_query($total_earnings_query);
$total_earnings_row = mysqli_fetch_object($total_earnings_result);
$total_earnings = $total_earnings_row->total ? $total_earnings_row->total : 0;

// Get approved vs pending referrals
$approved_count = 0;
$pending_count = 0;

// Store the result for later use
$referrals = [];


// Reset the result pointer for the table display
$childs_left = get_single_dimensional(get_child_levels_position($uid, 'L'));
?>

<style>
    body, #page-wrapper {
        background-color: #0b0e11;
    }
    .content-header {
        display: none;
    }

    .referral-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        /*max-width: 1200px;*/
    }

    /* Referral Header */
    .referral-header {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .referral-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .referral-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .referral-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .referral-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
    }

    .referral-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.08);
    }

    .stat-label {
        font-size: 14px;
        color: #848e9c;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .stat-label i {
        margin-right: 8px;
        color: #f0b90b;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
    }

    .stat-value.earnings {
        color: #0ecb81;
    }

    /* Referral Card */
    .referral-card {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        margin-bottom: 25px;
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .card-header {
        background: rgba(0, 0, 0, 0.2);
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header h3 {
        margin: 0;
        font-size: 16px;
        color: #f0b90b;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .referral-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .referral-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 13px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .referral-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
    }

    .referral-table tr:last-child td {
        border-bottom: none;
    }

    .referral-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-approved {
        background-color: rgba(14, 203, 129, 0.1);
        color: #0ecb81;
    }

    .status-pending {
        background-color: rgba(246, 70, 93, 0.1);
        color: #f6465d;
    }

    /* User Info */
    .user-info {
        display: flex;
        align-items: center;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(45deg, #f0b90b, #f8d33a);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #0b0e11;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }

    .user-name {
        font-weight: 500;
        color: #fff;
    }

    .user-id {
        font-size: 12px;
        color: #848e9c;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .referral-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .referral-header {
            padding: 20px;
        }

        .referral-table {
            display: block;
            overflow-x: auto;
        }

        .referral-stats {
            grid-template-columns: 1fr;
        }
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>
<div class="referral-wrapper">


    <!-- Referral List Card -->
    <div class="referral-card">
        <div class="card-header">
            <h3><i class="fas fa-list"></i> Referral List</h3>
        </div>
        <div class="card-body">
            <table class="referral-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>User</th>
                        <th>Contact</th>
                        <th>Joined</th>
                        <?php /*<th>Sponsor</th>
                        <th>Placement</th>
                        <th>Position</th>*/?>
                        <th>Package</th>
                       
                        <?php /*<th>Position</th>*/?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = my_fetch_object($result)){
                             


                            $i++;?>
                            <tr>
                                <td><?php echo $i;?></td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar"><?php echo substr($row->name, 0, 1); ?></div>
                                        <div class="user-details">
                                            <span class="user-name"><?php echo $row->name;?></span>
                                            <span class="user-id"><?php echo $row->login_id;?></span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-details">
                                        <span class="user-name"><?php echo $row->mobile;?></span>
                                    </div>
                                </td>
                                <td><?php echo date("d M, Y", strtotime($row->datetime));?></td>
                                <?php /*<td><?php echo $row->sponsor;?></td>
                                <td><?php echo $row->placement;?></td>
                                <td><?php echo $row->position;?></td>*/?>
                                <td><span class="stat-value"><?php echo $row->topup*1;?></span></td>
                                
                                <?php /*<td><?php echo (in_array($row->uid, $childs_left)) ? 'L' : 'R';?></td>*/?>
                            </tr>
                            <?php }?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php include_once 'footer.php'; ?>