<?php
$title = "Team";
include_once 'header.php';
$i=0;
$j=0;
$child_levels = get_child_levels($uid, $with='yes');

// Calculate total team members
$total_members = 0;
foreach ($child_levels as $level) {
    $total_members += count($level);
}

// Calculate levels
$total_levels = count($child_levels);

// Calculate total earnings (example calculation)
$total_earnings = 0;
foreach ($child_levels as $level) {
    $uids = implode(",", $level);
    if (!empty($uids)) {
        $earnings_query = "SELECT SUM(topup) as total FROM user WHERE uid IN ($uids)";
        $earnings_result = my_query($earnings_query);
        $earnings_row = mysqli_fetch_object($earnings_result);
        $total_earnings += ($earnings_row->total ? $earnings_row->total : 0);
    }
}


?>

<style>
    body, #page-wrapper {
        background-color: #0b0e11;
    }
    .content-header {
        display: none;
    }

    .team-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        /*max-width: 1200px;*/
    }

    /* Team Header */
    .team-header {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .team-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .team-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: radial-gradient(ellipse at bottom, rgba(240, 185, 11, 0.1), transparent 70%);
        pointer-events: none;
    }

    .team-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .team-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .team-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
    }

    .team-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
        z-index: -1;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .stat-label {
        font-size: 14px;
        color: #848e9c;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .stat-label i {
        margin-right: 8px;
        color: #f0b90b;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        display: flex;
        align-items: baseline;
    }

    .stat-value.earnings {
        color: #0ecb81;
    }

    .stat-value .currency {
        font-size: 14px;
        margin-right: 4px;
        opacity: 0.7;
    }

    .stat-value .unit {
        font-size: 14px;
        margin-left: 4px;
        opacity: 0.7;
    }

    .stat-trend {
        font-size: 12px;
        margin-left: 8px;
        display: flex;
        align-items: center;
    }

    .trend-up {
        color: #0ecb81;
    }

    .trend-down {
        color: #f6465d;
    }

    /* Team Card */
    .team-card {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        margin-bottom: 25px;
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .team-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .card-header {
        background: rgba(0, 0, 0, 0.2);
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header h3 {
        margin: 0;
        font-size: 16px;
        color: #f0b90b;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-header-actions {
        display: flex;
        gap: 10px;
    }

    .card-filter {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        color: #eaecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-filter:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .team-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .team-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .team-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .team-table tr:last-child td {
        border-bottom: none;
    }

    .team-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-approved {
        background-color: rgba(14, 203, 129, 0.1);
        color: #0ecb81;
        border: 1px solid rgba(14, 203, 129, 0.2);
    }

    .status-pending {
        background-color: rgba(246, 70, 93, 0.1);
        color: #f6465d;
        border: 1px solid rgba(246, 70, 93, 0.2);
    }

    /* User Info */
    .user-info {
        display: flex;
        align-items: center;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(45deg, #f0b90b, #f8d33a);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #0b0e11;
        font-weight: bold;
        margin-right: 10px;
        position: relative;
        overflow: hidden;
    }

    .user-avatar::after {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
        transform: rotate(45deg);
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }

    .user-name {
        font-weight: 500;
        color: #fff;
    }

    .user-id {
        font-size: 12px;
        color: #848e9c;
    }

    /* Level Badge */
    .level-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: linear-gradient(45deg, #f0b90b, #f8d33a);
        color: #0b0e11;
        font-weight: bold;
        font-size: 12px;
    }

    /* Package Value */
    .package-value {
        font-weight: 600;
        color: #f0b90b;
    }

    /* Pagination */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        padding: 10px;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        overflow: hidden;
    }

    .pagination li {
        border-right: 1px solid rgba(255, 255, 255, 0.05);
    }

    .pagination li:last-child {
        border-right: none;
    }

    .pagination a {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        color: #eaecef;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 40px;
    }

    .pagination a:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .pagination .active a {
        background: rgba(240, 185, 11, 0.2);
        color: #f0b90b;
    }

    /* Empty State */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #848e9c;
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: rgba(240, 185, 11, 0.3);
    }

    .empty-text {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .empty-subtext {
        font-size: 14px;
        opacity: 0.7;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .team-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .team-header {
            padding: 20px;
        }

        .team-table {
            display: block;
            overflow-x: auto;
        }

        .team-stats {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .card-header-actions {
            width: 100%;
            justify-content: space-between;
        }
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(240, 185, 11, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0);
        }
    }
</style>
<div class="team-wrapper">
    <!-- Team Header with Stats -->
    <div class="team-header">
        <div class="team-title">
            <h2><i class="fas fa-users"></i> My Team</h2>
        </div>
        <div class="team-stats">
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-user-friends"></i> Team Members</div>
                <div class="stat-value"><?php echo $total_members; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-layer-group"></i> Levels</div>
                <div class="stat-value"><?php echo $total_levels; ?></div>
            </div>
           
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-coins"></i> Total Value</div>
                <div class="stat-value earnings"><span class="currency">$</span><?php echo number_format($total_earnings, 2); ?></div>
            </div>
        </div>
    </div>

    <!-- Team List Card -->
    <div class="team-card">
        <div class="card-header">
            <h3><i class="fas fa-list"></i> Team Members</h3>
            <div class="card-header-actions">
                <select class="card-filter">
                    <option value="all">All Levels</option>
                    <?php for ($l = 0; $l < count($child_levels); $l++) { ?>
                        <option value="<?php echo $l; ?>">Level <?php echo $l; ?></option>
                    <?php } ?>
                </select>
               
            </div>
        </div>
        <div class="card-body">
            <table class="team-table">
                <thead>
                    <tr>
                        <th width="50">#</th>
                        <th>Member</th>
                        <th>Joined</th>
                        <th>Placement</th>
                        <?php /*<th>Sponsor</th>*/?>
                        <?php /*<th>Position</th>*/?>
                        <th>Package</th>
                        <th>Level</th>
                        
                    </tr>
                        </thead>
           <tbody>
    <?php foreach ($child_levels as $key => $child_level){
        $uids = implode(" , ", $child_level);
        if(!$uids){$uids = 0;}
        $query = "SELECT u.uid, u.login_id, u.name, u.mobile, u.datetime, s.uid as sponsoruid, s.login_id as sponsor, p.uid as placementuid, p.login_id as placement, u.position, u.package, u.topup FROM user as u"
            . " LEFT JOIN user as s ON s.uid=u.refer_id"
            . " LEFT JOIN user as p ON p.uid=u.placement_id"
            . " WHERE u.uid IN ($uids)";

        $result = my_query($query);
        while ($row = my_fetch_object($result)){
       


        $i++;?>
        <tr>
            <td><?php echo $i;?></td>
            <td>
                <div class="user-info">
                    <div class="user-avatar"><?php echo substr($row->name, 0, 1); ?></div>
                    <div class="user-details">
                        <span class="user-name"><?php echo $row->name;?></span>
                        <span class="user-id"><?php echo $row->login_id;?></span>
                    </div>
                </div>
            </td>
            <td><?php echo date("d M, Y", strtotime($row->datetime));?></td>
            <td>
                <div class="user-details">
                    <span class="user-id"><?php echo $row->placementuid;?></span>
                </div>
            </td>
            <td><span class="package-value"><?php echo $row->topup*1;?></span></td>
            <td><div class="level-badge"><?php echo $j;?></div></td>
            
        </tr>
        <?php }$j++;}?>
                    </tbody>
                </table>

                <!-- Empty state (will only show if there are no team members) -->
                <?php if ($total_members == 0): ?>
                <div class="empty-state">
                    <div class="empty-icon"><i class="fas fa-users"></i></div>
                    <div class="empty-text">You don't have any team members yet</div>
                    <div class="empty-subtext">Start building your team by sharing your referral link with potential members</div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <ul class="pagination">
                    <li><a href="#"><i class="fas fa-angle-double-left"></i></a></li>
                    <li><a href="#"><i class="fas fa-angle-left"></i></a></li>
                    <li class="active"><a href="#">1</a></li>
                    <li><a href="#">2</a></li>
                    <li><a href="#">3</a></li>
                    <li><a href="#"><i class="fas fa-angle-right"></i></a></li>
                    <li><a href="#"><i class="fas fa-angle-double-right"></i></a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php include_once 'footer.php'; ?>